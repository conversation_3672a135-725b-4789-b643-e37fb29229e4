{"ACCEPTED": "ACCEPTED", "ARRIVED": "ARRIVED", "AppName": "App Name", "AppleStoreLink": "Apple Store Link", "Balance": "Balance", "CANCELLED": "CANCELLED", "COMPLETE": "COMPLETE", "CardPaymentAmount": "Paid by Card -", "CashPaymentAmount": "Paid by Cash -", "CompanyName": "Company Name", "CompanyWebsite": "Company Website", "Customer_paid": "Customer paid", "Discounts": "Discounts", "FacebookHandle": "Facebook Page Link", "Gross_trip_cost": "Gross trip cost", "InstagramHandle": "Instagram Page Link", "NEW": "NEW", "PAID": "PAID", "PAYMENT_PENDING": "PAYMENT PENDING", "PENDING": "PENDING", "PlayStoreLink": "Play Store Link", "Profit": "Profit", "REACHED": "REACHED", "STARTED": "STARTED", "TwitterHandle": "Twitter Page Link", "WalletPayment": "Paid by <PERSON><PERSON>", "about_us": "About Us", "about_us_content1": "We are the largest mobility platform and one of the world's largest online on-demand services provider.", "about_us_content2": "Manage bookings, request quotes, or book a online service with our simple and quick online booking system. We are an on-demand services company that allows guests to easily book various services online. We offer the best services in the country. ", "about_us_menu": "About Us", "accept": "ACCEPT", "accept_booking_request": " accepted your booking request.", "account_approve": "Account Approved", "account_create_successfully": "Account created successfully", "actions": "Actions", "active_booking": "ACTIVE BOOKING", "active_car_delete": "Active Car cannot be deleted.", "active_status": "Active Status", "add": "ADD", "addMoneyTextInputPlaceholder": "Add Amount", "add_language": "Edit Language", "add_money": "Add Money", "add_to_wallet": "Add to Wallet", "addbookinglable": "Add Bookings", "admin": "Admin", "admin_contact": "Contact your admin to approve account", "advance_settings": "Advance Settings", "alert": "<PERSON><PERSON>", "alert_text": "<PERSON><PERSON>", "all": "All", "alladmin": "All Admins", "allow_del_final_img": "Allow delivery drop image", "allow_del_pkp_img": "Allow delivery pick image", "allow_location": "Allow Location for the Realtime Map", "allow_multi_country": "Allow Multi Country Selection", "amount": "Amount", "android": "Android", "app_info": "App Information", "app_link": "App Link : ", "apple_signin_error": "Apple SignIn is not set up in developer.appple.com or you have used the same email to SignUp already.", "apply": "APPLY", "apply_promo": "Apply Promo", "approved": "Approved", "assign": "ASSIGN", "assign_driver": "Assign Driver", "auth_error": "Auth Error: Please check your entered credentials", "auto_dispatch": "Auto Dispatch", "back": "BACK TO LOGIN", "bankAccount": "Bank Account", "bankCode": "Bank Code", "bankName": "Bank Name", "bank_fields": "Bank Reg Fields", "base_fare": "Base Fare", "bill_details": "<PERSON>", "bill_details_title": "<PERSON>", "blank_message": "No Records To Display", "body": "Body", "book": "Book", "book_later": "BOOK LATER", "book_later_button": "BOOK LATER", "book_now": "BOOK NOW", "book_now_button": "BOOK NOW", "book_ride": "Book Your Ride", "book_your_ride_menu": "Make a Booking", "book_your_title": "Book your Service", "booked_cab_title": "Manage Booking", "bookingPayment": "Booking Payment", "booking_cancelled": "Booking is cancelled. ID : ", "booking_count": "Booking Count", "booking_date": "Booking Date", "booking_date_time": "Booking Data Time", "booking_history": "Booking History", "booking_id": "Booking ID", "booking_ref": "Booking Reference", "booking_request": "Booking Requests", "booking_status": "BOOKING STATUS", "booking_success": "Booking successful. Booking Id : ", "booking_title": "MY BOOKINGS", "camera": "Camera", "camera_permission_error": "Camera Permission Error", "cancel": "CANCEL", "cancelSlab": "Cancellation Slabs", "cancel_booking": "Cancel Booking", "cancel_confirm": "Do you want really cancel?", "cancel_messege1": "Your Job with Booking Id", "cancel_messege2": "has been cancelled successfully", "cancel_reason_modal_title": "Reason", "cancel_ride": "CANCEL BOOKING", "cancellationFee": "Cancellation Fee", "cancellation_reason": "Cancellation Reason", "cancellation_reasons": "Cancellation Reasons", "cancelled_incomplete_booking": "CANCELLED INCOMPLETE BOOKING", "carApproved_by_admin": "Active car not approved by admin.", "car_add": "Add car for accept booking", "car_approval": "Car Approval", "car_horn_repeat": "Repeat sound on new trip", "car_no_not_found": "Vehicle number not assigned", "car_type": "Vehicle Type", "car_type_blank_error": "Select vehicle type", "car_view_horizontal": "Car list view Horizontal", "card_payment_amount": "Card payment amount", "cars": "Cars", "cash_on_delivery": "CASH ON DELIVERY", "cash_payment_amount": "Cash payment amount", "chat_blank": "please write something...", "chat_input_title": "Type something nice...", "chat_not_found": "No chat history found", "chat_requested": ": Chat Message", "chat_title": "Cha<PERSON>", "choose_image_first": "Choose image first.", "close": "CLOSE", "code_colon": "Code : ", "complete_ride": "COMPLETE JOB", "confirm": "CONFIRM", "confirm_booking": "Confirm Booking", "contact_email": "Contact Email", "contact_input_error": "Please enter a valid email or mobile number.", "contact_placeholder": "Mobile Number Or Email", "contact_us": "Contact Us", "convenience_fee": "Convenience Fees", "convenience_fee_type": "Convenience Fee Type", "convert_button": "Convert", "convert_to_driver": "Convert to <PERSON>", "convert_to_mile": "Convert to <PERSON>", "convert_to_rider": "Convert To <PERSON>", "country_blank_error": "Please select the country", "country_restriction": "Autocomplete Country Restriction", "createdAt": "Create Date", "credited": "CREDITED", "currency_code": "Currency Code", "currency_settings": "<PERSON><PERSON><PERSON><PERSON>", "currency_symbol": "Currency Symbol", "customer_name": "Customer Name", "dashboard_text": "Dashboard", "dateLocale": "Date Local", "date_time": "Date & Time", "debited": "DEBITED", "delete": "Delete", "delete_account_lebel": "Delete Account", "delete_account_modal_subtitle": "Do you want to delete your account ?", "delete_account_modal_title": "Confirmation", "delete_account_msg": "User can remove all their data from the system by deleting the account from profile page in mobile app.", "delete_account_para1": "All your data will be purged from the system. Your profile image, email, phone number, social logins including Google login and all booking history, everything will be permanently removed.", "delete_account_para2": "Deleted user data and account are irrecoverable.", "delete_account_subheading": "Once you delete your account:", "delete_message": "ARE YOU SURE YOU WANT TO DELETE THIS ROW?", "deliveryDetailMissing": "Please enter receiving person's Name & Phone No.", "deliveryInstructions": "BOOKING INSTRUCTIONS", "deliveryPerson": "Receiving Person Name", "deliveryPersonPhone": "Receiving Person Phone No", "delivery_information": "Booking Information", "demo_mode": "Restricted in Demo App.", "description": "Description", "device_type": "Device Type", "disclaimer": "Disclaimer", "disclaimer_text": "This app collects location data to enable 'Search Vehicle Location', 'Travel Route and Navigation', 'Realtime Vehicle Movement', even when the app is closed or not in use.", "discount": "Discount", "discount_ammount": "Discount Amount", "distance": "DISTANCE", "dont_cancel_text": "Don't Cancel", "driver": "Driver", "driverRadius": "<PERSON>", "driver_active": "Driver Active Status", "driver_assign_messege": "Driver will Assign Soon..", "driver_cancelled_booking": "DRIVER CANCELLED BOOKING", "driver_completed_ride": "You have reached destination. Please complete the payment.", "driver_earning": "Driver Earning History", "driver_finding_alert": "Finding Drivers for you", "driver_journey_msg": "Driver has started. Your booking id is ", "driver_name": "Driver Name", "driver_near": "Driver Near You", "driver_setting": "Driver Settings", "drivers": "Drivers", "drop_address": "Drop address", "drop_location": "Drop Location", "drop_location_blank_error": "Select drop location", "earning_amount": "Earning Amount", "earning_reports": "Earning Reports", "edit": "Edit", "edit_json": "<PERSON>", "editcar": "Edit Car", "email": "Email", "email_or_mobile_issue": "Please check your email id or mobile number. Note: Mobile number should be in International format e.g. +919999888877", "email_placeholder": "Email", "emergency": "Emergency", "end_date": "End Date", "enter_code": "Enter the code", "estimate_fare_text": "This is your estimate fare only", "export": "Export", "extra_info": "Extra Information", "facebook_login_auth_error": "Facebook Login Error:", "first_name": "First Name", "first_name_placeholder": "First Name", "first_page_tooltip": "First Page", "firstname": "First Name", "flat": "Flat", "fleetadmins": "Fleet Admins", "follow_facebook": "Follow us on Facebook", "follow_instagram": "Follow us on Instagram", "follow_twitter": "Follow us on Twitter", "general_settings": "GENERAL SETTINGS", "get_estimate": "Get Estimate", "go_online_to_accepting_jobs": "Start accepting jobs.", "go_to_booking": "GO TO BOOKING", "google_places_error": "Place ID to Location Error", "gross_earning": "Gross Earnings", "home": "Home", "ignore_job_title": "Do you want to ignore this job?", "ignore_text": "IGNORE", "image": "Image", "image_size_warning": "(Image size: Max 2 MB)", "image_upload_error": "Image upload error", "incomeText": "My Earnings", "incomplete_user": "User profile is incomplete.", "ios": "iOS", "km": "km", "landing_slogan": "Best Services Guaranteed", "langLocale": "Language Locale", "langName": "Language Name", "language": "Languages", "last_name": "Last Name", "last_name_placeholder": "Last Name", "last_page_tooltip": "Last Page", "lastname": "Last Name", "license_image": "License Image", "license_image_back": "License image back", "license_image_front": "License image front", "loading": "Loading...", "locationServiveBody": "Background location is running...", "locationServiveTitle": "Location Update", "location_fetch_error": "Location fetch error", "location_permission_error": "Location Permission Error", "login": "<PERSON><PERSON>", "login_error": "Authentication error", "login_signup": "Login / Sign Up", "logout": "Logout", "make_active": "Make Active", "make_default": "<PERSON>", "map_screen_drop_input_text": "Drop Where ?", "map_screen_where_input_text": "Where From ?", "max_limit": "Max Discount Allowed", "medialibrary": "Media Library", "message": "Write your message", "mile": "mi", "min_fare": "Minimum Fare", "min_limit": "Minimum Order Value", "min_order_value": "Minimum order value", "mins": "mins", "minsDelayed": "Minutes Delayed", "mobile": "Mobile Number", "mobile_apps_on_store": "Mobile Apps available on App Stores", "mobile_need_update": "Please input your Mobile Number for placing a booking.", "mobile_no_blank_error": "Please enter a valid mobile number.", "modal_close": "Modal has been closed.", "months": "Months", "must_login": "Please Login for Booking", "my_rides_menu": "My Bookings", "my_wallet_menu": "My Wallet", "my_wallet_tile": "My Wallet", "myaccount": "My Account", "name": "Name", "navigation_available": "Map Navigation is only available when booking is ACCEPTED or STARTED", "negative_balance": "Allow Driver Negative Balance", "new_booking_notification": "You Have A New Booking Request", "next_page_tooltip": "Next Page", "no": "No", "no_bookings": "No bookings available.", "no_cancel_reason": "No cancel reasons available.", "no_car_assign_text": "No vehicle assign", "no_cars": "No cars available.", "no_details_error": "Please fill up all the details properly.", "no_driver_found_alert_OK_button": "OK", "no_driver_found_alert_messege": "Sorry,No Drivers found right now.Please try again later", "no_driver_found_alert_title": "<PERSON><PERSON>!", "no_name": "No Name", "no_provider_found": "No Payment Providers Found", "no_tasks": "No tasks found for Driver", "no_user_match": "No Users Matched", "not_available": "Unavailable", "not_found_text": "Not Found", "not_logged_in": "Not Logged In", "not_valid_user_type": "Usertype is not valid.", "notification_title": "You have a notification", "of": "of", "ok": "OK", "on_duty": "On Duty", "options": "OPTIONS", "order_id": "OrderId ", "order_no": "Order No : ", "other_info": "Other Vehicle or Driver Info", "otp": "OTP :", "otp_blank_error": "OTP can't be blank", "otp_here": "Please enter OTP here", "otp_sms": " is the OTP. Thank you.", "otp_validate_error": "OTP is not valid", "panic_num": "Panic Dial Number", "panic_question": "Do you really want to make a Panic Call?", "panic_text": "Panic Call", "parcel_option": "Parcel Option", "parcel_type": "PARCEL TYPE", "parcel_types": "PARCEL TYPES", "password": "Password", "past_booking_error": "Book Later is not available for Past DateTime or within next 15 mins.", "payWithCard": "Pay With Card", "pay_cash": "PAY WITH CASH", "pay_mode": "Payment Mode", "payable_ammount": "Payable Amount", "payaed_ammount": "Payed Amount", "payment": "Payment", "payment_cancelled": "Payment Cancelled", "payment_fail": "Your Payment Failed", "payment_gateway": "Payment Gateway", "payment_mode": "PAYMENT MODE", "payment_of": "Your Payment of ", "payment_status": "Payment Status", "payment_thanks": "Thank you for your payment.", "paynow_button": "Pay Now", "percentage": "Percentage", "phone": "Phone", "phone_no_update": "Please update your Phone number in the Edit Profile section before making a booking", "pickUpInstructions": "PICKUP INSTRUCTION", "pickup_address": "Pickup Address", "pickup_location": "Pickup Location", "place_to_coords_error": "Place to Coordinate error. Please try again.", "previous_page_tooltip": "Previous Page", "privacy_policy": "Privacy Policy", "privacy_policy_change_privacy_para1": "We may update our Privacy Policy from time to time. Thus, you are advised to review this page periodically for any changes. We will notify you of any changes by posting the new Privacy Policy on this page.", "privacy_policy_change_privacy_para2": "This policy is effective as of 2023-12-12", "privacy_policy_children_para1": "These Services do not address anyone under the age of 13. We do not knowingly collect personally identifiable information from children under 13. In the case we discover that a child under 13 has provided us with personal information, we immediately delete this from our servers. If you are a parent or guardian and you are aware that your child has provided us with personal information, please contact us so that we will be able to do necessary actions.", "privacy_policy_contact_para1": "If you have any questions or suggestions about our Privacy Policy, do not hesitate to contact us at ", "privacy_policy_cookie_para1": "Cookies are files with a small amount of data that are commonly used as anonymous unique identifiers. These are sent to your browser from the websites that you visit and are stored on your device's internal memory.", "privacy_policy_cookie_para2": "This Service does not use these “cookies” explicitly. However, the app may use third party code and libraries that use “cookies” to collect information and improve their services. You have the option to either accept or refuse these cookies and know when a cookie is being sent to your device. If you choose to refuse our cookies, you may not be able to use some portions of this Service.", "privacy_policy_heading_change_privacy": "CHANGES TO THIS PRIVACY POLICY", "privacy_policy_heading_children": "CHILDREN’S PRIVACY", "privacy_policy_heading_contact": "CONTACT US", "privacy_policy_heading_cookie": "COOKIES", "privacy_policy_heading_info": "INFORMATION COLLECTION AND USE", "privacy_policy_heading_link": "LINKS TO OTHER SITES", "privacy_policy_heading_log": "LOG DATA", "privacy_policy_heading_security": "SECURITY", "privacy_policy_heading_service": "SERVICE PROVIDERS", "privacy_policy_info_list1": "First Name", "privacy_policy_info_list2": "Last Name", "privacy_policy_info_list3": "Email Address", "privacy_policy_info_list4": "Phone Number", "privacy_policy_info_list5": "Search vehicle location", "privacy_policy_info_list6": "Travel Route and Navigation", "privacy_policy_info_list7": "Real-time vehicle movement", "privacy_policy_info_para1": "For a better experience, while using our Service, we will require you to provide us with certain personally identifiable information, including but not limited to ", "privacy_policy_info_para2": "The information that we request will be retained by us and used as described in this privacy policy.", "privacy_policy_info_para3": "We do collect the following sensitive information when you use or Sign up on our App ", "privacy_policy_info_para4": "We collect your location data to enable", "privacy_policy_info_para5": "The app use third party services for social login like Google and Apple Login that collect information used to identify you. We capture User email and Name from social login if user has chosen it to be disclosed while performing a social login.", "privacy_policy_link_para1": "This Service may contain links to other sites. If you click on a third-party link, you will be directed to that site. Note that these external sites are not operated by us. Therefore, we strongly advise you to review the Privacy Policy of these websites. We have no control over and assume no responsibility for the content, privacy policies, or practices of any third-party sites or services.", "privacy_policy_log_para1": "We want to inform you that whenever you use our Service, in a case of an error in the app we collect data and information (through third party products) on your phone called Log Data. This Log Data may include information such as your device Internet Protocol (“IP”) address, device name, operating system version, the configuration of the app when utilizing our Service, the time and date of your use of the Service, and other statistics.", "privacy_policy_para1": "This page is used to inform visitors regarding our policies with the collection, use, and disclosure of Personal Information if anyone decided to use any of our Service.", "privacy_policy_para2": "If you choose to use our Service, then you agree to the collection and use of information in relation to this policy. The Personal Information that we collect is used for providing and improving the Service. We will not use or share your information with anyone except as described in this Privacy Policy.", "privacy_policy_para3": "The terms used in this Privacy Policy have the same meanings as in our Terms and Conditions, which is accessible at our all Apps unless otherwise defined in this Privacy Policy.", "privacy_policy_security_para1": "We value your trust in providing us your Personal Information, thus we are striving to use commercially acceptable means of protecting it. But remember that no method of transmission over the internet, or method of electronic storage is 100% secure and reliable, and we cannot guarantee its absolute security.", "privacy_policy_service_list1": "To facilitate our Service;", "privacy_policy_service_list2": "To provide the Service on our behalf;", "privacy_policy_service_list3": "To perform Service-related services; or", "privacy_policy_service_list4": "To assist us in analyzing how our Service is used.", "privacy_policy_service_para1": "We may employ third-party companies and individuals due to the following reasons:", "privacy_policy_service_para2": " We want to inform users of this Service that these third parties have access to your Personal Information. The reason is to perform the tasks assigned to them on our behalf. However, they are obligated not to disclose or use the information for any other purpose.", "processDate": "Process Date", "process_withdraw": "Process Withdraw", "processed": "Processed", "product_section_1": "You experience is important to us. We never compromise on our standards. Safety regulations are particularly emphasized to ensure peace of mind.", "product_section_2": "View live tracking, price estimations, and even book ahead of time! We have every avenue covered with every latest technology.", "product_section_4": "Aside from cash, credit card, payments are PCI DSS compliant. The website has an SSL certification, and the apps are checked by the Apple Store and Google Play.", "product_section_3": "Driver can add many type of vehicles.He/She can set one vehicle as default for booking", "product_section_heading": "Service Information", "product_section_para": "We provide the best App based services in the country.", "profile": "Profile", "profile_title": "PROFILE", "profile_image": "Profile Image", "profile_incomplete": "Profile incomplete. Go to Profile Settings.", "profile_page_subtitle": "Profile Details", "profile_page_title": "My Profile", "profile_setting_menu": "Profile Settings", "profile_updated": "Profile Updated.", "promo": "Promos", "promo_apply": "(Promo Apply)", "promo_discount": "Promo Discount", "promo_discount_value": "Promo Value", "promo_eligiblity": "Sorry! Promo is not valid as bill amount is lower.", "promo_exp": "Sorry! Promo has expired.", "promo_exp_limit": "Sorry! Promo usage limit over.", "promo_name": "Promo Name", "promo_offer": "Promo and Offers", "promo_usage": "Promo Count Available", "promo_used": "Sorry! You have already used the promo code.", "promo_used_by": "Promo Used By Count", "proper_bonus": "PLease enter proper bonus in numbers.", "proper_email": "Please enter email properly.", "proper_input_image": "Upload Proper Image", "proper_input_licenseimage": "Upload proper License Image", "proper_input_name": "Enter proper name", "proper_input_vehicleno": "Enter proper vehicle number", "provider_fetch_error": "Unable to fetch payment providers", "provider_not_found": "No Payment Providers Found.", "pruduct_section_heading_1": "Comfortable", "pruduct_section_heading_2": "Convenient", "pruduct_section_heading_3": "Vehicle Management", "pruduct_section_heading_4": "Secure", "push_error_1": "Failed to get push token for push notification!", "push_error_2": "Must use physical device for Push Notifications", "push_notification_title": "Push Notifications", "queue": "Busy", "rate_per_hour": "Rate Per Hour", "rate_per_unit_distance": "Distance Rate Per (Km or Mile)", "rate_ride": "Rate Your Driver", "real_time_driver_section_text": "Drivers Realtime", "reason": "Reason", "receipt": "Receipt", "received_rating": "You received a X star rating", "refer_earn": "Refer & Earn", "referer_not_found": "Referer Id not found", "referralId": "Referral Id", "referral_bonus": "Referral Bonus", "referral_id_placeholder": "Referral Id (Optional)", "reg_error": "Error while SignUp", "register": "Register", "register_as_driver": "Register User?", "register_button": "Register Now", "registration_title": "Registration", "remove_promo": "Remove Promo", "requestDate": "Request Date", "request_otp": "Sign In With OTP", "request_payment": "Request Payment", "require_approval": "Your account requires approval from Admin", "ride_details_page_title": "Booking Details", "ride_list_title": "My Bookings", "customer": "Customer", "rider_cancel_text": "Booking Cancel", "rider_not_here": "NO REQUESTS AT THE MOMENT", "rider_withdraw": "Customer <PERSON>w", "riders": "Customers", "roundTrip": "ROUND TRIP", "rows": "Rows", "save": "SAVE", "search": "Search", "search_for_an_address": "Search for an address", "searching": "Searching", "security_title": "Security Settings", "selectBid": "Select Bid", "select_car": "Select Vehicle Type", "select_country": "Select Country", "select_driver": "Select Driver", "select_proper": "Please select properly.", "select_reason": "Select Cancellation Reason", "select_user": "Select User", "send_button_text": "Send", "senderPersonPhone": "Booking Person Phone No", "service_off": "YOUR SERVICE STATUS IS 'OFF'", "set_decimal": "Set <PERSON>", "settings_label3": "Booking OTP", "settings_label4": "<PERSON> Approval", "settings_label5": "Car Approval", "settings_title": "Settings", "share_msg": "Hi, Use this code while registering and get Bonus of ", "share_msg_no_bonus": "Hi, Check out this App. I am loving it.", "show_live_route": "Show Live Route", "signup_via_referral": "SignUp Via Referral", "skip": "<PERSON><PERSON>", "spacer_message": "OR CONNECT WITH", "start_trip": "START TRIP", "subject": "Subject", "submit": "SUBMIT", "submit_rating": "SUBMIT RATING", "success": "Success", "success_payment": "Payment done successfully.", "swipe_symbol": "Swipe Symbol Direction", "take_deliver_image": "DELIVERY IMAGE", "take_pickup_image": "PICKUP IMAGE", "task_list": "Home", "delivery_task_list": "DELIVERY TASKS", "terms": "Privacy Policy", "thanks": "Thanks For Rating", "this_month_text": "Month", "thismonth": "THIS MONTH", "title": "Title", "today_text": "Today", "total": "Total", "total_fare": "Total Fare", "total_time": "Total Time", "totalearning": "Total Earnings", "transaction_history_title": "WALLET TRANSACTION HISTORY", "transaction_id": "Transaction Id :", "tripInstructions": "TRIP INSTRUCTIONS", "trip_cost": "Trip Cost", "trip_cost_driver_share": "Driver Share", "trip_end_time": "Trip End Time", "trip_start_time": "Trip Start Time", "try_again": "Please try again.", "type": "Type", "update_any": "You cannot update Email and Mobile at same time.", "update_button": "Update Now", "update_failed": "Update Failed", "update_profile_title": "Update Document", "updated": "Updated", "upload_car_image": "Upload Car Image", "upload_driving_license": "Upload your Driving License", "upload_driving_license_back": "Upload your Driving License Back", "upload_driving_license_front": "Upload your Driving License Front", "use_distance_matrix": "Use Distance Matrix API", "use_wallet_balance": "Use Wallet Cash (Your balance is ", "user": "Users", "user_exists": "User with same Email or Mobile number exists.", "user_issue_contact_admin": "No profile data found. Please contact Admin", "user_type": "User Type", "users_title": "Users", "usertype": "User Type", "valid_amount": "Please enter a valid amount", "vehicle_model_name": "Vehicle Make / Brand Name", "vehicle_make": "Make", "vehicle_model_no": "Vehicle Model No", "vehicle_model": "Model", "vehicle_number": "Number", "vehicle_no": "Vehicle Number", "vehicle_reg_no": "Vehicle Registration Number", "verify_otp": "Verify OTP", "wallet": "Wallet", "wallet_balance": "Wallet Balance", "wallet_balance_low": "Your wallet balance is too low.", "wallet_balance_zero": "You Wallet Balance is 0. If you take Payment in Cash, the Admin Convenience Fee will get deducted from Wallet and your Wallet Balance will be in Negative after the Job. Negative balance will restrict further Jobs. You need to Recharge Wallet or Ask Admin for help.", "wallet_balance_zero_customer": "You wallet balance is negative. You are not allowed to book with negative balance", "wallet_ballance": "Wallet Balance", "wallet_money_field": "Wallet Denominations", "wallet_payment_amount": "Wallet payment amount", "wallet_title": "Finance", "wallet_updated": "Wallet Updated successfully.", "wallet_zero": "Wallet Balance 0.", "was_successful": " was successful", "where_are_you": "You Are Here", "withdraw": "WITHDRAW", "withdraw_below_zero": "Withdraw amount cannot be less than or equal to 0.", "withdraw_money": "Withdraw Money", "withdraw_more": "Withdraw amount cannot be greater than Wallet Balance.", "withdrawn": "WITHDRAWN", "withdraws": "WITHDRAWS", "year": "Years", "yes": "Yes", "you_are_offline": "Your duty is off", "you_rated_text": "Driver Rating", "your_fare": "Your Fare", "your_promo": "Your promo", "your_trip": "Your Trip", "lang": "Language", "lang1": "Lang:", "check_mobile": "Check your massage box", "check_email": " Check your email inbox", "not_on_same_time": "Email or Mobile and License images can't change at the same time.", "app_store_deception": "Your business needs to be in safe hands at all times. We ensure you never run out of customers and not run at loss. We are trusted by over 500+ companies to deliver quality marketing campaigns using Digital marketing & Offline marketing channels.", "on": "ON", "offline": "You're offline", "online": "You're online", "off": "OFF", "total_cumtomer": "No. of Customer", "total_drivers": "No. of Drivers", "active_driver": "Active Drivers", "completed_bookings": "Complete Booking", "cancelled_bookings": "Cancel Booking", "booking_chart": "Booking Chart", "report": "Reports", "app_store_deception1": "The App and for Customer and Driver, not for the Admin Login. Take 2 phones and install in each. Register a Customer in One and register a Driver in One. For the Driver always select Location Always Allow when login in. Then creating a booking from One phone and receiving on the other where driver logged in. Use the provided User login on the Web link to see the Admin side of things.", "driver_distance": "Driver will arrive in", "fill_email_first": "Add email first then take document image", "position": "List Position", "disable_online": "Disable Online Payments", "disable_cash": "Disable Cash Payments", "cash": "Cash", "card": "Card", "wallet_bal_low": "Wallet balance low.", "signIn": "SignIn", "cash_booking_false": "Admin can't create a booking when cash payment is disable in system.", "wallet_booking_alert": "You cannot book another by using Wallet. You have an existing wallet booking.", "complete_payment": "COMPLETE PAYMENT", "force_end": "Force End", "verify_id": "Id / Passport Number", "upload_verifyIdImage": "Upload your ID / Passport Number", "settings_label6": "Verify Id / Passport", "verifyid_image": "Id / Passport Image", "verifyid_error": "Sorry you have no ID / Passport", "upload_id_details": "Upload Your ID / Passport", "best_service_provider": "BEST SERVICE PROVIDED", "service_start_soon": "Our service will start soon...", "always_on": "Location needs Always On", "fix": "Fix It", "personal_info": "Personal Info", "documents": "Documents", "driving_license_font": "Driving License Font", "driving_license_back": "Driving License Back", "password_blank_messege": "password not blank", "password_alphaNumeric_check": "Password required alphaNumeric", "password_complexity_check": "Password complexity", "confirm_password_not_match_err": "Confirm password not match", "forgot_password": "Forgot password?", "carType_required": "Car Is Required", "term_required": "Term Required", "confirm_password": "Confirm Password", "license_image_required": "License Image Required", "no_payment_getways": "No payment getways", "term_condition": "Terms & Conditions", "allow_only": "Location needs while using app", "email_use": "Email <PERSON>cation Mode", "not_registred": "This email is not registered", "set_link_email": "Reset password link send to above mail", "term_condition_para1": "Thank you for choosing our app-based booking services for taxi, cab, and delivery needs. Please read the following terms and conditions carefully before using our services:", "term_condition_heading1": "Acceptance of Terms :", "term_condition_para2": "By using our services, you agree to be bound by these Terms and Conditions. If you do not agree to these Terms and Conditions, you may not use our services.", "term_condition_heading2": "Eligibility :", "term_condition_para3": "You must be at least 18 years old to use our services. By using our services, you represent and warrant that you are at least 18 years old.", "term_condition_heading3": "Service Description :", "term_condition_para4": "Our app-based booking services provide a platform to connect users with taxi, cab, and delivery providers. We do not own, operate, or control any of the vehicles or drivers that use our platform. Our services are intended to be used for personal and non-commercial use only.", "term_condition_heading4": "Payment :", "term_condition_para5": "Users are required to pay for services provided through our platform. We accept various forms of payment, including credit cards, debit cards, and electronic payment methods. All payments made through our platform are subject to our payment policies, which may be updated from time to time.", "term_condition_heading5": "User Conduct :", "term_condition_para6": "Users are expected to use our services responsibly and not engage in any behavior that may harm our platform, drivers, or other users. Users are not permitted to use our platform for any unlawful or unauthorized purpose. Users are also prohibited from interfering with the operation of our platform or engaging in any activity that could compromise the security or integrity of our platform.", "term_condition_heading6": "User Content :", "term_condition_para7": "Users are solely responsible for the content they submit through our platform. By submitting content, users grant us a non-exclusive, royalty-free, transferable, and sublicensable license to use, modify, and reproduce the content for the purpose of providing our services.", "term_condition_heading7": "Intellectual Property :", "term_condition_para8": "Our platform, including all content and intellectual property, is owned by us and/or our licensors. Users may not copy, modify, distribute, or reproduce any of the content or intellectual property without our prior written consent.", "term_condition_heading8": "Disclaimers :", "term_condition_para9": "We do not guarantee the availability, reliability, or quality of any services provided by drivers using our platform. We are not responsible for any loss or damage that may result from the use of our services. Our platform is provided 'as is' and without any warranties, express or implied.", "term_condition_heading9": "Limitation of Liability :", "term_condition_para10": "In no event shall we be liable for any indirect, incidental, special, or consequential damages arising out of or in connection with the use of our services, whether or not we have been advised of the possibility of such damages.", "term_condition_heading10": "Termination :", "term_condition_para11": "We reserve the right to terminate a users access to our platform at any time, without notice, for any reason.", "term_condition_heading11": "Governing Law :", "term_condition_para12": "These Terms and Conditions shall be governed by and construed in accordance with the laws of the jurisdiction where the company is based.", "term_condition_heading12": "Amendments :", "term_condition_para13": "We reserve the right to update these Terms and Conditions at any time, without notice. Users are advised to review these Terms and Conditions periodically to stay informed of any changes.", "term_condition_para14": "If you have any questions or concerns about these Terms and Conditions, please contact us at ", "create_new_user": "Creating new user, please wait...", "delete_your_car": "Do you want to delete your car?", "start_accept_bid": "You Have Receive One Bid", "phone_error_msg": "*Please write Phone Number with your country code. Ex.: +449876543210", "password_must_not_contain_whitespaces": "Password must not contain Whitespaces.", "password_must_have_at_least_one_uppercase_character": "Password must have at least one Uppercase Character.", "password_must_have_at_least_one_lowercase_character": "Password must have at least one Lowercase Character.", "password_must_contain_at_least_one_digit": "Password must contain at least one Digit.", "password_must_be_8-16_characters_long": "Password must be 8-16 Characters Long.", "realtime_drivers": "Driver Live Location", "prepaid": "Prepaid", "fromEmail": "From Email", "smtpsettings": "SMTP Settings", "host": "Host", "port": "Port", "username": "Username", "secure": "Secure", "saved_address": "Saved Address", "no_saved_address": "No Saved Address", "work": "Work", "other": "Other", "hours": "Hours", "first_admin_deleted": "First admin cannot be deleted", "smtp_error": "Please check your SMTP details", "driver_accept_low_cost_capacity_booking": "Driver accept low cost/capacity booking", "email_login": "<PERSON><PERSON>", "mobile_login": "Mobile Login", "mobile_number": "Enter Mobile Number", "email_id": "Enter Email Id", "email_send": "Email Send", "login_settings": "<PERSON><PERSON>", "mobile_or_email_cant_off": "Mobile and Email login cannot be disabled at a time", "social_login": "Social Login", "select_payment_getway": "Select Payment Getway", "accepted_booking": "Your booking is accepted", "not_chat": "You can not chat right now.", "booking_is": "Your booking is ", "not_call": "You can not call right now.", "customer_contact": "Customer contact", "driver_contact": "Driver contact", "sos": "Sos", "sos_title": "SOS", "your_feedback": "Your feedback (optional) ...", "your_feedback_test": "Your feedback will help us improve driving experience better.", "how_your_trip": "How is your trip?", "rate_for": "Rate for", "stops": "Stops", "goto_home": "Goto Home", "customer_info": "Customer Info", "upload_profile_image": "Upload Profile Image", "not_approved": "Not Approved", "bookings_table": "Bookings Table", "ride_information": "Ride Info", "trip_start_date": "Trip Start Date", "trip_instruction": "Trip Instruction", "customer_id": "Customer ID", "customer_email": "Customer <PERSON><PERSON>", "driver_id": "Driver ID", "driver_email": "Driver Email", "device_id": "Device ID", "driver_share": "Driver Share", "fleet_admin_fee": "Fleet Admin Fee", "payment_info": "Payment Info", "add_customer": "Add Customer", "add_driver": "Add Driver", "add_fleetadmin": "Add Fleet Admin", "add_admin": "Add Admin", "go_back": "Go Back", "info": "Info", "rides": "Rides", "next_slide": "Next", "get_started": "Get startted", "best_services": "Best Services", "best_experience": "Best Experience", "value_for_money": "Value For Money", "make_changes_to_update": "Make changes to Update", "hidden_demo": "Hidden for <PERSON><PERSON>", "update_admin": "<PERSON><PERSON><PERSON>", "update_fleetAdmin": "Upadate Fleet Admin", "proper_mobile": "Please enter mobile properly.", "approve_status": "Approve Status", "create_rider": "Create Rider", "create_driver": "Create Driver", "check_approve_status": "Check Approve Status", "driver_active_staus_choose": "Driver Active Staus Cho<PERSON>", "add_car": "Add Car", "add_notification": "Add Notifications", "driver_required": "Driver Required", "vehicleNumber_required": "Vehicle Reg No. Required", "vehicleMake_required": "Vehicle Make/Brand Name Required", "vehicleModel_required": "Vehicle Model Required", "daily": "DAILY", "thisyear": "THIS YEAR", "car_details_title": "Vehicle Details", "position_required": "Please Choose List position", "convenience_fee_type_required": "Please Choose Convenience Fee Type", "base_fare_required": "Please Choose Base Fare", "convenience_fee_required": "Please Choose Convenience Fees", "min_fare_required": "Please Choose Minimum Fare", "rate_per_hour_required": "Please Choose Rate Per Hour", "rate_per_unit_distance_required": "Please Choose Distance Rate Per (Km or Mile)", "promo_code": "PROMO CODE", "booking_details": "Booking Details", "no_active_booking": "No active booking", "no_cancelled_booking": "No cancelled booking ", "booking_successful": "Booking Successful", "booking_confirm": "Your booking has been confirmed", "done": "DONE", "smssettings": "SMS Settings", "customMobileOTP": "Enable Custom Mobile OTP", "apiUrl": "Api Url", "method": "Method", "authorization": "Authorization", "contentType": "Content-Type", "show_in_list": "Show in list", "code": "Code", "time": "TIME", "cost": "COST", "no_data_available": "No Data Available", "withdraws_web": "Withdraws", "driver_info": "Driver Info", "add_to_review": "Add to review", "riders_title": "CUSTOMERS", "drivers_title": "DRIVERS", "fleetadmins_title": "FLEET ADMINS", "alladmins_title": "ALL ADMINS", "add_admin_title": "ADD ADMIN", "add_driver_title": "ADD DRIVER", "add_fleetadmin_title": "ADD FLEET ADMIN", "add_booking_title": "ADD BOOKINGS", "add_car_title": "ADD CAR", "add_cartype_title": "ADD VEHICLE", "add_to_wallet_title": "ADD TO WALLET", "add_notification_title": "ADD NOTIFICATION", "add_customer_title": "ADD CUSTOMER", "bookings_table_title": "BOOKINGS TABLE", "cars_title": "CARS", "car_type_title": "VEHICLE TYPE", "app_info_title": "APP INFORMATION", "cancellation_reasons_title": "CANCELLATION REASON", "active_car": "Active Car", "other_cars": "Other Cars", "earning_reports_title": "EARNING REPORTS", "push_notifications_title": "PUSH NOTIFICATIONS", "promo_offer_title": "PROMO AND OFFERS", "update_admin_title": "UPDATE ADMIN", "update_car_title": "UPDATE CAR", "update_carType_title": "UPDATE CAR TYPE", "update_fleetAdmin_title": "UPDATE FLEET ADMIN", "documents_title": "DOCUMENTS", "my_wallet_title": "WALLET", "Withdraw_title": "WITHDRAWS", "smtpsettings_title": "SMTP SETTINGS", "smssettings_title": "SMS SETTINGS", "language_cap": "LANGUAGES", "push_notifications": "Push Notifications", "general_settings_title": "GENERAL SETTINGS", "driver_earning_title": "DRIVER EARNING HISTORY", "add_carType": "Add Vehicle Type", "promo_not_found": "This promo code not found", "driver_threshold": "<PERSON>", "complain_date": "Complain Date", "complain": "<PERSON><PERSON><PERSON>", "message_text": "Message", "pending": "Pending", "solved": "Solved", "complain_title": "COMPLAIN", "login_otp": "LOG IN WITH OTP", "signup": "Sign Up", "company_phone": "Company Phone", "fleet_admin_fee_required": "Fleet Admin <PERSON>e Required", "fleet_admin_comission": "Fleetadmin Commission", "marker_title_1": "Pickup", "marker_title_2": "Drop", "marker_title_3": "Stops", "contact": "Contact", "select_date": "Please Select Date", "within_min": "min", "wallet_balance_threshold_reached": "Your Wallet Balance Threshold Reached", "booking_status_web": "Booking Status", "parcel_type_web": "Parcel Type", "take_pickup_image_web": "Pickup Image", "take_deliver_image_web": "Delivery Image", "distance_web": "Distance", "pickUpInstructions_web": "Pickup Instruction", "payment_mode_web": "Payment Mode", "promo_code_web": "Promo Code", "feedback": "<PERSON><PERSON><PERSON>", "bankDetails": "BANK DETAILS", "trip_date_time": "Trip Date Time", "date": "Date", "email_msg": "We hope you enjoyed your ride with us.", "country_1": "Country", "update_promo_title": "UPDATE PROMO", "add_promo_title": "ADD PROMO", "add_carType_title": "ADD CAR TYPE", "update_driver_title": "UPDATE DRIVER", "update_customer_title": "UPDATE CUSTOMER", "payment_settings": "Payment Settings", "page_not_not_found": "Page Not Found", "go_to_home": "Go to Home", "page_not_not_found_text": "Sorry! The page you are looking for is no longer available or has been removed.", "code_already_avilable": "This Promo Already Available", "no_active_car": "No Active Car Available", "no_others_car": "No Others Car Available", "booking_type": "Booking Type", "system_price_with_bid": "System Price With Bid", "system_price": "System Price", "bid": "Bid", "booking_flow": "Booking Flow", "end_time": "End Time", "otherPerson": "Other Person Name", "otherPersonPhone": "Other Person Phone", "for_other_person": "Booking for other person", "otherPersonDetailMissing": "Please enter other person's Name & Phone No.", "otherPerson_title": "Other Person Details", "referral_email_used": "This Email already used a referal Id", "referral_number_used": "This Number already used a referal Id", "pickup_address_from_map": "Select pickup address from map", "drop_address_from_map": "Select drop address from map", "drag_map": "Drag map to select address", "promo_name_error": "Fill promo name", "promo_code_error": "Fill promo code", "promo_description_error": "Fill promo description", "promo_discount_value_error": "Fill promo discount value", "promo_discount_type_error": "Fill promo discount type", "max_promo_discount_value_error": "Fill max promo discount value", "min_order_error": "Fill minimum order value", "flat_minorder_maxorder": "In promo type of flat your minimum order value can not exceed max discount.", "in_demo_mobile_login": "Mobile login is disabled in demo mode", "fleetadmin_earning_reports": "<PERSON><PERSON><PERSON>", "fleetadmin_name": "Fleetadmin Name", "fleetadmin_id": "Fleetadmin ID", "car_image": "Car Image", "id": "ID", "amount_must_be_gereater_than_100": "For Slickpay amount must be greater than 100"}