import {
  FET<PERSON>_DELIVERY_TASKS,
  FETCH_DELIVERY_TASKS_SUCCESS,
  FETCH_DELIVERY_TASKS_FAILED,
  UPDATE_PRODUCT_STATUS,
  UPDATE_PRODUCT_STATUS_SUCCESS,
  UPDATE_PRODUCT_STATUS_FAILED,
  CLEAR_DELIVERY_TASKS
} from "../store/types";

const INITIAL_STATE = {
  tasks: null,
  customers: null,
  loading: false,
  updating: false,
  error: {
    flag: false,
    msg: null
  },
  updateError: {
    flag: false,
    msg: null
  }
}

export const deliverytasksreducer = (state = INITIAL_STATE, action) => {
  switch (action.type) {
    case FETCH_DELIVERY_TASKS:
      return {
        ...state,
        loading: true,
        error: {
          flag: false,
          msg: null
        }
      };
    case FETCH_DELIVERY_TASKS_SUCCESS:
      return {
        ...state,
        tasks: action.payload.tasks,
        customers: action.payload.customers,
        loading: false,
        error: {
          flag: false,
          msg: null
        }
      };
    case FET<PERSON>_DELIVERY_TASKS_FAILED:
      return {
        ...state,
        tasks: null,
        customers: null,
        loading: false,
        error: {
          flag: true,
          msg: action.payload
        }
      };
    case UPDATE_PRODUCT_STATUS:
      return {
        ...state,
        updating: true,
        updateError: {
          flag: false,
          msg: null
        }
      };
    case UPDATE_PRODUCT_STATUS_SUCCESS:
      // Actualizar el estado del producto en las tareas locales
      const updatedTasks = state.tasks ? state.tasks.map(task => {
        if (task.orderId === action.payload.orderId) {
          return {
            ...task,
            products: task.products.map(product => 
              product.id === action.payload.productId 
                ? { ...product, ...action.payload.productData }
                : product
            )
          };
        }
        return task;
      }) : null;

      // Actualizar el estado del cliente en la lista de clientes
      const updatedCustomers = state.customers ? state.customers.map(customer => {
        const customerTasks = updatedTasks ? updatedTasks.filter(task => task.customer.id === customer.id) : [];
        const totalProducts = customerTasks.reduce((sum, task) => sum + task.products.length, 0);
        const deliveredProducts = customerTasks.reduce((sum, task) => 
          sum + task.products.filter(p => p.status === 'DELIVERED').length, 0);
        
        return {
          ...customer,
          totalProducts,
          deliveredProducts,
          progress: totalProducts > 0 ? (deliveredProducts / totalProducts) * 100 : 0
        };
      }) : null;

      return {
        ...state,
        tasks: updatedTasks,
        customers: updatedCustomers,
        updating: false,
        updateError: {
          flag: false,
          msg: null
        }
      };
    case UPDATE_PRODUCT_STATUS_FAILED:
      return {
        ...state,
        updating: false,
        updateError: {
          flag: true,
          msg: action.payload
        }
      };
    case CLEAR_DELIVERY_TASKS:
      return INITIAL_STATE;
    default:
      return state;
  }
};
