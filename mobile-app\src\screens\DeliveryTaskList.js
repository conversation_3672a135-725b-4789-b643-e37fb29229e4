import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    FlatList,
    TouchableOpacity,
    StyleSheet,
    RefreshControl,
    Alert,
    ActivityIndicator
} from 'react-native';
import { Header, SearchBar } from 'react-native-elements';
import { Ionicons } from '@expo/vector-icons';
import { useSelector, useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { colors } from '../common/theme';
import ProductChecklistItem from '../components/ProductChecklistItem';
import ProductDeliveryModal from '../components/ProductDeliveryModal';
import { api } from 'common';

const DeliveryTaskList = ({ navigation }) => {
    const { t } = useTranslation();
    const dispatch = useDispatch();
    
    // Redux state
    const auth = useSelector(state => state.auth);
    const bookingData = useSelector(state => state.bookinglistdata);
    const deliveryTasksData = useSelector(state => state.deliverytasksdata);

    // API functions
    const {
        fetchDeliveryTasksFromBooking,
        clearDeliveryTasks
    } = api;
    
    // Local state
    const [customers, setCustomers] = useState([]);
    const [filteredCustomers, setFilteredCustomers] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [refreshing, setRefreshing] = useState(false);
    const [loading, setLoading] = useState(true);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [updating, setUpdating] = useState(false);

    useEffect(() => {
        loadDeliveryData();
    }, [bookingData]);

    useEffect(() => {
        filterCustomers();
    }, [searchQuery, deliveryTasksData.customers]);

    useEffect(() => {
        // Actualizar customers cuando cambien los datos de Redux
        if (deliveryTasksData.customers) {
            setCustomers(deliveryTasksData.customers);
        }
    }, [deliveryTasksData.customers]);

    const loadDeliveryData = () => {
        try {
            // Buscar el booking activo con delivery_orders
            const activeBooking = bookingData.active?.find(booking =>
                booking.delivery_orders || booking.delivery_type === 'MULTIPLE_ORDERS'
            );

            if (activeBooking) {
                dispatch(fetchDeliveryTasksFromBooking(activeBooking.id));
            } else {
                setLoading(false);
                Alert.alert('Info', 'No se encontraron órdenes de delivery activas');
            }
        } catch (error) {
            console.error('Error loading delivery data:', error);
            Alert.alert(t('error'), t('error_loading_data'));
        } finally {
            setLoading(false);
        }
    };

    const filterCustomers = () => {
        const customersToFilter = deliveryTasksData.customers || [];
        if (!searchQuery.trim()) {
            setFilteredCustomers(customersToFilter);
        } else {
            const filtered = customersToFilter.filter(customer =>
                customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                customer.phone.includes(searchQuery) ||
                customer.address.toLowerCase().includes(searchQuery.toLowerCase())
            );
            setFilteredCustomers(filtered);
        }
    };

    const handleRefresh = async () => {
        setRefreshing(true);
        // Aquí podrías recargar los datos desde el servidor
        await new Promise(resolve => setTimeout(resolve, 1000));
        loadDeliveryData();
        setRefreshing(false);
    };

    const handleProductDeliveryConfirm = (product) => {
        setSelectedProduct(product);
        setModalVisible(true);
    };

    const handleProductStatusChange = async (productId, status, deliveryData) => {
        try {
            // Encontrar el producto y su orden
            let routeId = null;
            let orderId = null;

            // Buscar en las tareas de delivery
            if (deliveryTasksData.tasks) {
                for (const task of deliveryTasksData.tasks) {
                    const product = task.products.find(p => p.id === productId);
                    if (product) {
                        orderId = task.orderId;
                        // Aquí necesitarías obtener el routeId del booking activo
                        const activeBooking = bookingData.active?.find(booking =>
                            booking.delivery_orders || booking.delivery_type === 'MULTIPLE_ORDERS'
                        );
                        routeId = activeBooking?.routeId || activeBooking?.id;
                        break;
                    }
                }
            }

            if (routeId && orderId) {
                // Usar la acción de Redux para actualizar el estado
                await dispatch(api.updateProductDeliveryStatus(routeId, orderId, productId, {
                    status,
                    ...deliveryData
                }));

                // Mostrar mensaje de éxito si no hay error
                if (!deliveryTasksData.updateError.flag) {
                    Alert.alert(t('success'), t('product_status_updated'));
                }
            } else {
                throw new Error('No se pudo encontrar la información de la ruta u orden');
            }
        } catch (error) {
            console.error('Error updating product status:', error);
            Alert.alert(t('error'), error.message || t('error_updating_status'));
        }
    };

    const handleModalConfirm = async (deliveryData) => {
        await handleProductStatusChange(
            deliveryData.productId,
            deliveryData.status,
            deliveryData
        );
        setModalVisible(false);
        setSelectedProduct(null);
    };

    const handleModalCancel = () => {
        setModalVisible(false);
        setSelectedProduct(null);
    };

    const navigateToCustomerDetail = (customer) => {
        navigation.navigate('CustomerProductList', { customer });
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'COMPLETED':
                return colors.GREEN;
            case 'PARTIAL':
                return colors.YELLOW;
            case 'PENDING':
            default:
                return colors.GREY;
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'COMPLETED':
                return 'checkmark-circle';
            case 'PARTIAL':
                return 'checkmark-circle-outline';
            case 'PENDING':
            default:
                return 'ellipse-outline';
        }
    };

    const renderCustomerItem = ({ item }) => (
        <TouchableOpacity
            style={styles.customerCard}
            onPress={() => navigateToCustomerDetail(item)}
        >
            <View style={styles.customerHeader}>
                <View style={styles.customerInfo}>
                    <Text style={styles.customerName}>{item.name}</Text>
                    <Text style={styles.customerAddress} numberOfLines={2}>
                        {item.address}
                    </Text>
                </View>
                <View style={styles.statusContainer}>
                    <Ionicons
                        name={getStatusIcon(item.progress === 100 ? 'COMPLETED' : item.progress > 0 ? 'PARTIAL' : 'PENDING')}
                        size={24}
                        color={getStatusColor(item.progress === 100 ? 'COMPLETED' : item.progress > 0 ? 'PARTIAL' : 'PENDING')}
                    />
                </View>
            </View>
            
            <View style={styles.progressContainer}>
                <View style={styles.progressInfo}>
                    <Text style={styles.progressText}>
                        {item.deliveredProducts}/{item.totalProducts} {t('products_delivered')}
                    </Text>
                    <Text style={styles.statusText}>
                        {item.progress === 100 ? t('completed') :
                         item.progress > 0 ? t('in_progress') : t('pending')}
                    </Text>
                </View>
                <View style={styles.progressBar}>
                    <View
                        style={[
                            styles.progressFill,
                            {
                                width: `${item.progress}%`,
                                backgroundColor: getStatusColor(item.progress === 100 ? 'COMPLETED' : item.progress > 0 ? 'PARTIAL' : 'PENDING')
                            }
                        ]}
                    />
                </View>
            </View>

            <View style={styles.customerActions}>
                <Ionicons name="chevron-forward" size={20} color={colors.GREY} />
            </View>
        </TouchableOpacity>
    );

    if (deliveryTasksData.loading || loading) {
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.BLUE} />
                <Text style={styles.loadingText}>{t('loading_delivery_data')}</Text>
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <Header
                centerComponent={{
                    text: t('delivery_task_list'),
                    style: { color: colors.WHITE, fontSize: 18, fontWeight: 'bold' }
                }}
                leftComponent={{
                    icon: 'arrow-back',
                    color: colors.WHITE,
                    onPress: () => navigation.goBack()
                }}
                backgroundColor={colors.BLUE}
            />

            <SearchBar
                placeholder={t('search_customers')}
                onChangeText={setSearchQuery}
                value={searchQuery}
                containerStyle={styles.searchContainer}
                inputContainerStyle={styles.searchInputContainer}
                inputStyle={styles.searchInput}
                searchIcon={{ color: colors.GREY }}
                clearIcon={{ color: colors.GREY }}
            />

            <View style={styles.summaryContainer}>
                <Text style={styles.summaryText}>
                    {filteredCustomers.length} {t('customers')} • {' '}
                    {filteredCustomers.filter(c => c.status === 'COMPLETED').length} {t('completed')}
                </Text>
            </View>

            <FlatList
                data={filteredCustomers}
                renderItem={renderCustomerItem}
                keyExtractor={(item) => item.customerId}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={[colors.BLUE]}
                    />
                }
                contentContainerStyle={styles.listContainer}
                showsVerticalScrollIndicator={false}
            />

            <ProductDeliveryModal
                visible={modalVisible}
                product={selectedProduct}
                onConfirm={handleModalConfirm}
                onCancel={handleModalCancel}
                loading={updating}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.LIGHT_GREY,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: colors.WHITE,
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
        color: colors.GREY,
    },
    searchContainer: {
        backgroundColor: colors.WHITE,
        borderBottomColor: colors.LIGHT_GREY,
        borderTopColor: colors.LIGHT_GREY,
        paddingHorizontal: 16,
        paddingVertical: 8,
    },
    searchInputContainer: {
        backgroundColor: colors.LIGHT_GREY,
        borderRadius: 8,
    },
    searchInput: {
        fontSize: 16,
        color: colors.BLACK,
    },
    summaryContainer: {
        backgroundColor: colors.WHITE,
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: colors.LIGHT_GREY,
    },
    summaryText: {
        fontSize: 14,
        color: colors.GREY,
        textAlign: 'center',
    },
    listContainer: {
        paddingVertical: 8,
    },
    customerCard: {
        backgroundColor: colors.WHITE,
        marginHorizontal: 16,
        marginVertical: 4,
        borderRadius: 8,
        padding: 16,
        elevation: 2,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
    },
    customerHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    customerInfo: {
        flex: 1,
        marginRight: 12,
    },
    customerName: {
        fontSize: 16,
        fontWeight: 'bold',
        color: colors.BLACK,
        marginBottom: 4,
    },
    customerAddress: {
        fontSize: 14,
        color: colors.GREY,
        lineHeight: 18,
    },
    statusContainer: {
        alignItems: 'center',
    },
    progressContainer: {
        marginBottom: 8,
    },
    progressInfo: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    progressText: {
        fontSize: 14,
        color: colors.BLACK,
        fontWeight: '500',
    },
    statusText: {
        fontSize: 12,
        color: colors.GREY,
        textTransform: 'uppercase',
    },
    progressBar: {
        height: 4,
        backgroundColor: colors.LIGHT_GREY,
        borderRadius: 2,
        overflow: 'hidden',
    },
    progressFill: {
        height: '100%',
        borderRadius: 2,
    },
    customerActions: {
        alignItems: 'flex-end',
    },
});

export default DeliveryTaskList;
