import React, { useState, useEffect } from 'react';
import {
    Modal,
    View,
    Text,
    TextInput,
    TouchableOpacity,
    StyleSheet,
    Alert,
    Image,
    ScrollView,
    KeyboardAvoidingView,
    Platform
} from 'react-native';
import { Button } from 'react-native-elements';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { colors } from '../common/theme';
import { useTranslation } from 'react-i18next';

const ProductDeliveryModal = ({
    visible,
    product,
    onConfirm,
    onCancel,
    loading = false
}) => {
    const { t } = useTranslation();
    const [deliveredQuantity, setDeliveredQuantity] = useState('');
    const [notes, setNotes] = useState('');
    const [photo, setPhoto] = useState(null);
    const [status, setStatus] = useState('DELIVERED');

    useEffect(() => {
        if (product) {
            setDeliveredQuantity(product.quantity?.toString() || '1');
            setNotes(product.deliveryNotes || '');
            setPhoto(product.deliveryPhoto || null);
            setStatus('DELIVERED');
        }
    }, [product]);

    const handleTakePhoto = async () => {
        try {
            const { status } = await ImagePicker.requestCameraPermissionsAsync();
            if (status !== 'granted') {
                Alert.alert(t('permission_required'), t('camera_permission_message'));
                return;
            }

            const result = await ImagePicker.launchCameraAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: true,
                aspect: [4, 3],
                quality: 0.8,
            });

            if (!result.canceled && result.assets[0]) {
                setPhoto(result.assets[0].uri);
            }
        } catch (error) {
            console.error('Error taking photo:', error);
            Alert.alert(t('error'), t('photo_error'));
        }
    };

    const handleSelectFromGallery = async () => {
        try {
            const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
            if (status !== 'granted') {
                Alert.alert(t('permission_required'), t('gallery_permission_message'));
                return;
            }

            const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ImagePicker.MediaTypeOptions.Images,
                allowsEditing: true,
                aspect: [4, 3],
                quality: 0.8,
            });

            if (!result.canceled && result.assets[0]) {
                setPhoto(result.assets[0].uri);
            }
        } catch (error) {
            console.error('Error selecting photo:', error);
            Alert.alert(t('error'), t('photo_error'));
        }
    };

    const showPhotoOptions = () => {
        Alert.alert(
            t('select_photo'),
            t('choose_photo_source'),
            [
                { text: t('camera'), onPress: handleTakePhoto },
                { text: t('gallery'), onPress: handleSelectFromGallery },
                { text: t('cancel'), style: 'cancel' }
            ]
        );
    };

    const handleConfirm = () => {
        if (!deliveredQuantity || isNaN(deliveredQuantity) || parseInt(deliveredQuantity) < 0) {
            Alert.alert(t('error'), t('invalid_quantity'));
            return;
        }

        const quantity = parseInt(deliveredQuantity);
        const totalQuantity = product?.quantity || 1;

        if (quantity > totalQuantity) {
            Alert.alert(t('error'), t('quantity_exceeds_total'));
            return;
        }

        const finalStatus = quantity === totalQuantity ? 'DELIVERED' : 'PARTIAL';

        const deliveryData = {
            productId: product.id,
            status: finalStatus,
            deliveredQuantity: quantity,
            deliveryNotes: notes.trim(),
            deliveryPhoto: photo,
            deliveryTimestamp: new Date().toISOString()
        };

        onConfirm(deliveryData);
    };

    const handleCancel = () => {
        setDeliveredQuantity('');
        setNotes('');
        setPhoto(null);
        setStatus('DELIVERED');
        onCancel();
    };

    if (!product) return null;

    const totalQuantity = product.quantity || 1;
    const currentDelivered = parseInt(deliveredQuantity) || 0;
    const isPartial = currentDelivered > 0 && currentDelivered < totalQuantity;

    return (
        <Modal
            visible={visible}
            animationType="slide"
            transparent={true}
            onRequestClose={handleCancel}
        >
            <View style={styles.overlay}>
                <KeyboardAvoidingView 
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    style={styles.container}
                >
                    <View style={styles.modal}>
                        <ScrollView showsVerticalScrollIndicator={false}>
                            {/* Header */}
                            <View style={styles.header}>
                                <Text style={styles.title}>{t('confirm_delivery')}</Text>
                                <TouchableOpacity onPress={handleCancel} style={styles.closeButton}>
                                    <Ionicons name="close" size={24} color={colors.BLACK} />
                                </TouchableOpacity>
                            </View>

                            {/* Product Info */}
                            <View style={styles.productInfo}>
                                <Text style={styles.productName}>{product.name}</Text>
                                <Text style={styles.productSku}>SKU: {product.sku}</Text>
                                {product.description && (
                                    <Text style={styles.productDescription}>{product.description}</Text>
                                )}
                            </View>

                            {/* Quantity Section */}
                            <View style={styles.section}>
                                <Text style={styles.sectionTitle}>{t('quantity_delivered')}</Text>
                                <View style={styles.quantityContainer}>
                                    <TextInput
                                        style={styles.quantityInput}
                                        value={deliveredQuantity}
                                        onChangeText={setDeliveredQuantity}
                                        keyboardType="numeric"
                                        placeholder="0"
                                    />
                                    <Text style={styles.quantityTotal}>/ {totalQuantity}</Text>
                                </View>
                                {isPartial && (
                                    <Text style={styles.partialWarning}>
                                        {t('partial_delivery_warning')}
                                    </Text>
                                )}
                            </View>

                            {/* Photo Section */}
                            <View style={styles.section}>
                                <Text style={styles.sectionTitle}>{t('delivery_photo')}</Text>
                                {photo ? (
                                    <View style={styles.photoContainer}>
                                        <Image source={{ uri: photo }} style={styles.photo} />
                                        <TouchableOpacity 
                                            style={styles.changePhotoButton}
                                            onPress={showPhotoOptions}
                                        >
                                            <Text style={styles.changePhotoText}>{t('change_photo')}</Text>
                                        </TouchableOpacity>
                                    </View>
                                ) : (
                                    <TouchableOpacity 
                                        style={styles.addPhotoButton}
                                        onPress={showPhotoOptions}
                                    >
                                        <Ionicons name="camera" size={32} color={colors.BLUE} />
                                        <Text style={styles.addPhotoText}>{t('add_photo')}</Text>
                                    </TouchableOpacity>
                                )}
                            </View>

                            {/* Notes Section */}
                            <View style={styles.section}>
                                <Text style={styles.sectionTitle}>{t('delivery_notes')}</Text>
                                <TextInput
                                    style={styles.notesInput}
                                    value={notes}
                                    onChangeText={setNotes}
                                    placeholder={t('add_notes_placeholder')}
                                    multiline
                                    numberOfLines={3}
                                    textAlignVertical="top"
                                />
                            </View>

                            {/* Action Buttons */}
                            <View style={styles.buttonContainer}>
                                <Button
                                    title={t('cancel')}
                                    onPress={handleCancel}
                                    buttonStyle={[styles.button, styles.cancelButton]}
                                    titleStyle={styles.cancelButtonText}
                                />
                                <Button
                                    title={t('confirm')}
                                    onPress={handleConfirm}
                                    loading={loading}
                                    buttonStyle={[styles.button, styles.confirmButton]}
                                    titleStyle={styles.confirmButtonText}
                                />
                            </View>
                        </ScrollView>
                    </View>
                </KeyboardAvoidingView>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    container: {
        width: '90%',
        maxHeight: '80%',
    },
    modal: {
        backgroundColor: colors.WHITE,
        borderRadius: 12,
        padding: 20,
        maxHeight: '100%',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: colors.BLACK,
    },
    closeButton: {
        padding: 4,
    },
    productInfo: {
        marginBottom: 20,
        padding: 12,
        backgroundColor: colors.LIGHT_GREY,
        borderRadius: 8,
    },
    productName: {
        fontSize: 16,
        fontWeight: 'bold',
        color: colors.BLACK,
        marginBottom: 4,
    },
    productSku: {
        fontSize: 14,
        color: colors.GREY,
        marginBottom: 4,
    },
    productDescription: {
        fontSize: 14,
        color: colors.BLACK,
    },
    section: {
        marginBottom: 20,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: colors.BLACK,
        marginBottom: 8,
    },
    quantityContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    quantityInput: {
        borderWidth: 1,
        borderColor: colors.GREY,
        borderRadius: 6,
        padding: 12,
        fontSize: 16,
        width: 80,
        textAlign: 'center',
    },
    quantityTotal: {
        fontSize: 16,
        color: colors.BLACK,
        marginLeft: 8,
    },
    partialWarning: {
        fontSize: 12,
        color: colors.YELLOW,
        marginTop: 4,
        fontStyle: 'italic',
    },
    photoContainer: {
        alignItems: 'center',
    },
    photo: {
        width: 200,
        height: 150,
        borderRadius: 8,
        marginBottom: 8,
    },
    changePhotoButton: {
        padding: 8,
    },
    changePhotoText: {
        color: colors.BLUE,
        fontSize: 14,
    },
    addPhotoButton: {
        borderWidth: 2,
        borderColor: colors.BLUE,
        borderStyle: 'dashed',
        borderRadius: 8,
        padding: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    addPhotoText: {
        color: colors.BLUE,
        fontSize: 14,
        marginTop: 8,
    },
    notesInput: {
        borderWidth: 1,
        borderColor: colors.GREY,
        borderRadius: 6,
        padding: 12,
        fontSize: 14,
        minHeight: 80,
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 20,
    },
    button: {
        flex: 1,
        marginHorizontal: 8,
        paddingVertical: 12,
        borderRadius: 6,
    },
    cancelButton: {
        backgroundColor: colors.GREY,
    },
    confirmButton: {
        backgroundColor: colors.GREEN,
    },
    cancelButtonText: {
        color: colors.WHITE,
        fontWeight: 'bold',
    },
    confirmButtonText: {
        color: colors.WHITE,
        fontWeight: 'bold',
    },
});

export default ProductDeliveryModal;
