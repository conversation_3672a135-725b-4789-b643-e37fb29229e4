import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    FlatList,
    StyleSheet,
    Alert,
    TouchableOpacity,
    ScrollView
} from 'react-native';
import { <PERSON><PERSON>, But<PERSON> } from 'react-native-elements';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';
import { colors } from '../common/theme';
import ProductChecklistItem from '../components/ProductChecklistItem';
import ProductDeliveryModal from '../components/ProductDeliveryModal';

const CustomerProductList = ({ navigation, route }) => {
    const { t } = useTranslation();
    const { customer } = route.params;
    
    // Local state
    const [products, setProducts] = useState([]);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [updating, setUpdating] = useState(false);

    useEffect(() => {
        if (customer && customer.products) {
            setProducts(customer.products);
        }
    }, [customer]);

    const handleProductDeliveryConfirm = (product) => {
        setSelectedProduct(product);
        setModalVisible(true);
    };

    const handleProductStatusChange = async (productId, status, deliveryData) => {
        setUpdating(true);
        try {
            // Actualizar el producto localmente
            const updatedProducts = products.map(product => 
                product.id === productId 
                    ? { ...product, status, ...deliveryData }
                    : product
            );
            
            setProducts(updatedProducts);
            
            // Aquí llamarías al API para actualizar el estado del producto
            // await updateProductDeliveryStatus(productId, status, deliveryData);
            
            Alert.alert(t('success'), t('product_status_updated'));
        } catch (error) {
            console.error('Error updating product status:', error);
            Alert.alert(t('error'), t('error_updating_status'));
        } finally {
            setUpdating(false);
        }
    };

    const handleModalConfirm = async (deliveryData) => {
        await handleProductStatusChange(
            deliveryData.productId,
            deliveryData.status,
            deliveryData
        );
        setModalVisible(false);
        setSelectedProduct(null);
    };

    const handleModalCancel = () => {
        setModalVisible(false);
        setSelectedProduct(null);
    };

    const handleMarkAllDelivered = () => {
        Alert.alert(
            t('confirm_action'),
            t('mark_all_products_delivered_confirm'),
            [
                { text: t('cancel'), style: 'cancel' },
                { 
                    text: t('confirm'), 
                    onPress: () => markAllProductsDelivered() 
                }
            ]
        );
    };

    const markAllProductsDelivered = async () => {
        setUpdating(true);
        try {
            const updatedProducts = products.map(product => ({
                ...product,
                status: 'DELIVERED',
                deliveredQuantity: product.quantity,
                deliveryTimestamp: new Date().toISOString(),
                deliveryNotes: t('bulk_delivery_note')
            }));
            
            setProducts(updatedProducts);
            Alert.alert(t('success'), t('all_products_marked_delivered'));
        } catch (error) {
            console.error('Error marking all products as delivered:', error);
            Alert.alert(t('error'), t('error_updating_status'));
        } finally {
            setUpdating(false);
        }
    };

    const handleNavigateToCustomer = () => {
        // Navegar a la ubicación del cliente usando Google Maps
        const address = customer.deliveryAddress.address;
        const lat = customer.deliveryAddress.lat;
        const lng = customer.deliveryAddress.lng;
        
        if (lat && lng) {
            navigation.navigate('MapScreen', {
                destination: { lat, lng, address },
                customerName: customer.customerName
            });
        } else {
            Alert.alert(t('error'), t('no_coordinates_available'));
        }
    };

    const getDeliveryProgress = () => {
        const delivered = products.filter(p => p.status === 'DELIVERED').length;
        const total = products.length;
        return { delivered, total, percentage: total > 0 ? (delivered / total) * 100 : 0 };
    };

    const progress = getDeliveryProgress();
    const isAllDelivered = progress.delivered === progress.total;
    const hasPendingProducts = products.some(p => p.status === 'PENDING');

    const renderProductItem = ({ item }) => (
        <ProductChecklistItem
            product={item}
            onStatusChange={handleProductStatusChange}
            onDeliveryConfirm={handleProductDeliveryConfirm}
            disabled={updating}
        />
    );

    return (
        <View style={styles.container}>
            <Header
                centerComponent={{
                    text: customer.customerName,
                    style: { color: colors.WHITE, fontSize: 18, fontWeight: 'bold' }
                }}
                leftComponent={{
                    icon: 'arrow-back',
                    color: colors.WHITE,
                    onPress: () => navigation.goBack()
                }}
                rightComponent={{
                    icon: 'navigation',
                    color: colors.WHITE,
                    onPress: handleNavigateToCustomer
                }}
                backgroundColor={colors.BLUE}
            />

            <ScrollView style={styles.content}>
                {/* Customer Info Card */}
                <View style={styles.customerCard}>
                    <View style={styles.customerHeader}>
                        <View style={styles.customerInfo}>
                            <Text style={styles.customerName}>{customer.customerName}</Text>
                            {customer.customerPhone && (
                                <TouchableOpacity 
                                    style={styles.phoneContainer}
                                    onPress={() => {/* Llamar al cliente */}}
                                >
                                    <Ionicons name="call" size={16} color={colors.BLUE} />
                                    <Text style={styles.phoneText}>{customer.customerPhone}</Text>
                                </TouchableOpacity>
                            )}
                        </View>
                    </View>
                    
                    <View style={styles.addressContainer}>
                        <Ionicons name="location" size={16} color={colors.GREY} />
                        <Text style={styles.addressText}>{customer.deliveryAddress.address}</Text>
                    </View>

                    {/* Progress Section */}
                    <View style={styles.progressSection}>
                        <View style={styles.progressHeader}>
                            <Text style={styles.progressTitle}>{t('delivery_progress')}</Text>
                            <Text style={styles.progressText}>
                                {progress.delivered}/{progress.total} {t('products')}
                            </Text>
                        </View>
                        
                        <View style={styles.progressBar}>
                            <View 
                                style={[
                                    styles.progressFill,
                                    { 
                                        width: `${progress.percentage}%`,
                                        backgroundColor: isAllDelivered ? colors.GREEN : colors.BLUE
                                    }
                                ]} 
                            />
                        </View>
                        
                        {isAllDelivered && (
                            <View style={styles.completedBadge}>
                                <Ionicons name="checkmark-circle" size={16} color={colors.GREEN} />
                                <Text style={styles.completedText}>{t('delivery_completed')}</Text>
                            </View>
                        )}
                    </View>
                </View>

                {/* Quick Actions */}
                {hasPendingProducts && (
                    <View style={styles.quickActions}>
                        <Button
                            title={t('mark_all_delivered')}
                            onPress={handleMarkAllDelivered}
                            buttonStyle={styles.quickActionButton}
                            titleStyle={styles.quickActionText}
                            icon={
                                <Ionicons 
                                    name="checkmark-done" 
                                    size={20} 
                                    color={colors.WHITE} 
                                    style={{ marginRight: 8 }} 
                                />
                            }
                            loading={updating}
                        />
                    </View>
                )}

                {/* Products List */}
                <View style={styles.productsSection}>
                    <Text style={styles.sectionTitle}>{t('products_to_deliver')}</Text>
                    
                    <FlatList
                        data={products}
                        renderItem={renderProductItem}
                        keyExtractor={(item) => item.id}
                        scrollEnabled={false}
                        showsVerticalScrollIndicator={false}
                    />
                </View>
            </ScrollView>

            <ProductDeliveryModal
                visible={modalVisible}
                product={selectedProduct}
                onConfirm={handleModalConfirm}
                onCancel={handleModalCancel}
                loading={updating}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.LIGHT_GREY,
    },
    content: {
        flex: 1,
    },
    customerCard: {
        backgroundColor: colors.WHITE,
        margin: 16,
        borderRadius: 12,
        padding: 16,
        elevation: 3,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    customerHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    customerInfo: {
        flex: 1,
    },
    customerName: {
        fontSize: 20,
        fontWeight: 'bold',
        color: colors.BLACK,
        marginBottom: 8,
    },
    phoneContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    phoneText: {
        fontSize: 14,
        color: colors.BLUE,
        marginLeft: 4,
    },
    addressContainer: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 16,
    },
    addressText: {
        fontSize: 14,
        color: colors.BLACK,
        marginLeft: 8,
        flex: 1,
        lineHeight: 20,
    },
    progressSection: {
        borderTopWidth: 1,
        borderTopColor: colors.LIGHT_GREY,
        paddingTop: 16,
    },
    progressHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 8,
    },
    progressTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: colors.BLACK,
    },
    progressText: {
        fontSize: 14,
        color: colors.GREY,
    },
    progressBar: {
        height: 6,
        backgroundColor: colors.LIGHT_GREY,
        borderRadius: 3,
        overflow: 'hidden',
        marginBottom: 8,
    },
    progressFill: {
        height: '100%',
        borderRadius: 3,
    },
    completedBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 8,
    },
    completedText: {
        fontSize: 14,
        color: colors.GREEN,
        fontWeight: 'bold',
        marginLeft: 4,
    },
    quickActions: {
        marginHorizontal: 16,
        marginBottom: 16,
    },
    quickActionButton: {
        backgroundColor: colors.GREEN,
        borderRadius: 8,
        paddingVertical: 12,
    },
    quickActionText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    productsSection: {
        marginBottom: 20,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: colors.BLACK,
        marginHorizontal: 16,
        marginBottom: 12,
    },
});

export default CustomerProductList;
