import {
  FETCH_DELIVERY_TASKS,
  FETCH_DELIVERY_TASKS_SUCCESS,
  FETCH_DELIVERY_TASKS_FAILED,
  UPDATE_PRODUCT_STATUS,
  UPDATE_PRODUCT_STATUS_SUCCESS,
  UPDATE_PRODUCT_STATUS_FAILED,
  CLEAR_DELIVERY_TASKS
} from "../store/types";
import { firebase } from '../config/configureFirebase';
import { get, onValue, off } from "firebase/database";

export const clearDeliveryTasks = () => (dispatch) => {
  dispatch({
    type: CLEAR_DELIVERY_TASKS,
    payload: null,
  });
}

export const fetchDeliveryTasks = (routeId) => (dispatch) => {
  const { deliveryRoutesRef } = firebase;

  dispatch({
    type: FETCH_DELIVERY_TASKS,
    payload: null,
  });

  const routeRef = deliveryRoutesRef(routeId);
  
  const unsubscribe = onValue(routeRef, (snapshot) => {
    if (snapshot.exists()) {
      const routeData = snapshot.val();
      
      if (routeData && routeData.orders) {
        // Procesar las órdenes para crear la estructura de tareas
        const tasks = routeData.orders.map(order => ({
          orderId: order.orderId,
          customer: order.customer,
          products: order.products || [],
          status: order.status || 'ASSIGNED',
          deliveryAddress: order.deliveryAddress,
          deliveryInstructions: order.deliveryInstructions,
          estimatedDeliveryTime: order.estimatedDeliveryTime,
          actualDeliveryTime: order.actualDeliveryTime
        }));

        // Agrupar por cliente para crear la lista de clientes
        const customersMap = {};
        tasks.forEach(task => {
          const customerId = task.customer.id;
          if (!customersMap[customerId]) {
            customersMap[customerId] = {
              id: customerId,
              name: task.customer.name,
              phone: task.customer.phone,
              email: task.customer.email,
              address: task.deliveryAddress,
              totalProducts: 0,
              deliveredProducts: 0,
              progress: 0,
              orders: []
            };
          }
          
          customersMap[customerId].orders.push(task);
          customersMap[customerId].totalProducts += task.products.length;
          customersMap[customerId].deliveredProducts += task.products.filter(p => p.status === 'DELIVERED').length;
        });

        // Calcular progreso para cada cliente
        const customers = Object.values(customersMap).map(customer => ({
          ...customer,
          progress: customer.totalProducts > 0 ? (customer.deliveredProducts / customer.totalProducts) * 100 : 0
        }));

        dispatch({
          type: FETCH_DELIVERY_TASKS_SUCCESS,
          payload: {
            tasks,
            customers
          }
        });
      } else {
        dispatch({
          type: FETCH_DELIVERY_TASKS_FAILED,
          payload: 'No se encontraron órdenes de delivery para esta ruta'
        });
      }
    } else {
      dispatch({
        type: FETCH_DELIVERY_TASKS_FAILED,
        payload: 'Ruta de delivery no encontrada'
      });
    }
  }, (error) => {
    dispatch({
      type: FETCH_DELIVERY_TASKS_FAILED,
      payload: error.message
    });
  });

  return unsubscribe;
}

export const updateProductDeliveryStatus = (routeId, orderId, productId, statusData) => async (dispatch) => {
  dispatch({
    type: UPDATE_PRODUCT_STATUS,
    payload: null,
  });

  try {
    // Llamar al endpoint del backend para actualizar el estado
    const response = await fetch(`${firebase.functionsUrl}/updateProductDeliveryStatus`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        routeId,
        orderId,
        productId,
        ...statusData
      })
    });

    const result = await response.json();

    if (result.success) {
      dispatch({
        type: UPDATE_PRODUCT_STATUS_SUCCESS,
        payload: {
          orderId,
          productId,
          productData: {
            status: statusData.status,
            deliveredQuantity: statusData.deliveredQuantity,
            deliveryNotes: statusData.deliveryNotes,
            deliveryPhoto: statusData.deliveryPhoto,
            deliveryLocation: statusData.location,
            deliveryTimestamp: statusData.timestamp || Date.now()
          }
        }
      });
    } else {
      throw new Error(result.error || 'Error actualizando estado del producto');
    }
  } catch (error) {
    dispatch({
      type: UPDATE_PRODUCT_STATUS_FAILED,
      payload: error.message
    });
  }
}

// Función auxiliar para obtener las tareas de delivery desde el booking activo
export const fetchDeliveryTasksFromBooking = (bookingId) => (dispatch, getState) => {
  const { bookinglistdata } = getState();
  const activeBooking = bookinglistdata.active?.find(booking => booking.id === bookingId);
  
  if (activeBooking && activeBooking.delivery_orders) {
    // Si el booking tiene órdenes de delivery, procesarlas
    const tasks = activeBooking.delivery_orders.map(order => ({
      orderId: order.orderId,
      customer: order.customer,
      products: order.products || [],
      status: order.status || 'ASSIGNED',
      deliveryAddress: order.deliveryAddress,
      deliveryInstructions: order.deliveryInstructions,
      estimatedDeliveryTime: order.estimatedDeliveryTime,
      actualDeliveryTime: order.actualDeliveryTime
    }));

    // Agrupar por cliente
    const customersMap = {};
    tasks.forEach(task => {
      const customerId = task.customer.id;
      if (!customersMap[customerId]) {
        customersMap[customerId] = {
          id: customerId,
          name: task.customer.name,
          phone: task.customer.phone,
          email: task.customer.email,
          address: task.deliveryAddress,
          totalProducts: 0,
          deliveredProducts: 0,
          progress: 0,
          orders: []
        };
      }
      
      customersMap[customerId].orders.push(task);
      customersMap[customerId].totalProducts += task.products.length;
      customersMap[customerId].deliveredProducts += task.products.filter(p => p.status === 'DELIVERED').length;
    });

    const customers = Object.values(customersMap).map(customer => ({
      ...customer,
      progress: customer.totalProducts > 0 ? (customer.deliveredProducts / customer.totalProducts) * 100 : 0
    }));

    dispatch({
      type: FETCH_DELIVERY_TASKS_SUCCESS,
      payload: {
        tasks,
        customers
      }
    });
  } else {
    dispatch({
      type: FETCH_DELIVERY_TASKS_FAILED,
      payload: 'No se encontraron órdenes de delivery en este booking'
    });
  }
};
