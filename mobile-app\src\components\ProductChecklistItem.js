import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { CheckBox, Button } from 'react-native-elements';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../common/theme';
import { useTranslation } from 'react-i18next';

const ProductChecklistItem = ({ 
    product, 
    onStatusChange, 
    onDeliveryConfirm, 
    disabled = false,
    showCustomerInfo = false,
    customerName = ''
}) => {
    const { t } = useTranslation();
    const [isExpanded, setIsExpanded] = useState(false);

    const getStatusColor = (status) => {
        switch (status) {
            case 'DELIVERED':
                return colors.GREEN;
            case 'PARTIAL':
                return colors.YELLOW;
            case 'DAMAGED':
                return colors.RED;
            case 'PENDING':
            default:
                return colors.GREY;
        }
    };

    const getStatusIcon = (status) => {
        switch (status) {
            case 'DELIVERED':
                return 'checkmark-circle';
            case 'PARTIAL':
                return 'checkmark-circle-outline';
            case 'DAMAGED':
                return 'close-circle';
            case 'PENDING':
            default:
                return 'ellipse-outline';
        }
    };

    const getStatusText = (status) => {
        switch (status) {
            case 'DELIVERED':
                return t('delivered');
            case 'PARTIAL':
                return t('partial');
            case 'DAMAGED':
                return t('damaged');
            case 'PENDING':
            default:
                return t('pending');
        }
    };

    const handleStatusToggle = () => {
        if (disabled) return;
        
        if (product.status === 'PENDING') {
            // Si está pendiente, abrir modal de confirmación
            onDeliveryConfirm(product);
        } else if (product.status === 'DELIVERED') {
            // Si está entregado, volver a pendiente
            onStatusChange(product.id, 'PENDING', {
                deliveredQuantity: 0,
                deliveryNotes: '',
                deliveryPhoto: '',
                deliveryTimestamp: null
            });
        }
    };

    const deliveredQuantity = product.deliveredQuantity || 0;
    const totalQuantity = product.quantity || 1;
    const isPartialDelivery = deliveredQuantity > 0 && deliveredQuantity < totalQuantity;
    const isFullDelivery = deliveredQuantity >= totalQuantity;

    return (
        <View style={styles.container}>
            {/* Header del producto */}
            <TouchableOpacity 
                style={styles.headerContainer}
                onPress={() => setIsExpanded(!isExpanded)}
                disabled={disabled}
            >
                <View style={styles.leftSection}>
                    <TouchableOpacity 
                        onPress={handleStatusToggle}
                        disabled={disabled}
                        style={styles.statusButton}
                    >
                        <Ionicons 
                            name={getStatusIcon(product.status)} 
                            size={24} 
                            color={getStatusColor(product.status)} 
                        />
                    </TouchableOpacity>
                    
                    <View style={styles.productInfo}>
                        <Text style={styles.productName}>{product.name}</Text>
                        <Text style={styles.productSku}>SKU: {product.sku}</Text>
                        {showCustomerInfo && (
                            <Text style={styles.customerName}>{customerName}</Text>
                        )}
                    </View>
                </View>

                <View style={styles.rightSection}>
                    <View style={styles.quantityContainer}>
                        <Text style={styles.quantityText}>
                            {deliveredQuantity}/{totalQuantity}
                        </Text>
                        <Text style={styles.quantityLabel}>{t('delivered')}</Text>
                    </View>
                    
                    <Ionicons 
                        name={isExpanded ? 'chevron-up' : 'chevron-down'} 
                        size={20} 
                        color={colors.GREY} 
                    />
                </View>
            </TouchableOpacity>

            {/* Detalles expandibles */}
            {isExpanded && (
                <View style={styles.expandedContent}>
                    <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>{t('description')}:</Text>
                        <Text style={styles.detailValue}>{product.description || t('no_description')}</Text>
                    </View>
                    
                    {product.weight > 0 && (
                        <View style={styles.detailRow}>
                            <Text style={styles.detailLabel}>{t('weight')}:</Text>
                            <Text style={styles.detailValue}>{product.weight} kg</Text>
                        </View>
                    )}

                    <View style={styles.detailRow}>
                        <Text style={styles.detailLabel}>{t('status')}:</Text>
                        <Text style={[styles.detailValue, { color: getStatusColor(product.status) }]}>
                            {getStatusText(product.status)}
                        </Text>
                    </View>

                    {product.deliveryNotes && (
                        <View style={styles.detailRow}>
                            <Text style={styles.detailLabel}>{t('notes')}:</Text>
                            <Text style={styles.detailValue}>{product.deliveryNotes}</Text>
                        </View>
                    )}

                    {product.deliveryTimestamp && (
                        <View style={styles.detailRow}>
                            <Text style={styles.detailLabel}>{t('delivered_at')}:</Text>
                            <Text style={styles.detailValue}>
                                {new Date(product.deliveryTimestamp).toLocaleString()}
                            </Text>
                        </View>
                    )}

                    {/* Botones de acción */}
                    {!disabled && product.status === 'PENDING' && (
                        <View style={styles.actionButtons}>
                            <Button
                                title={t('mark_delivered')}
                                onPress={() => onDeliveryConfirm(product)}
                                buttonStyle={[styles.actionButton, { backgroundColor: colors.GREEN }]}
                                titleStyle={styles.actionButtonText}
                            />
                        </View>
                    )}

                    {!disabled && product.status === 'DELIVERED' && (
                        <View style={styles.actionButtons}>
                            <Button
                                title={t('mark_pending')}
                                onPress={() => handleStatusToggle()}
                                buttonStyle={[styles.actionButton, { backgroundColor: colors.GREY }]}
                                titleStyle={styles.actionButtonText}
                            />
                        </View>
                    )}
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: colors.WHITE,
        marginVertical: 4,
        marginHorizontal: 8,
        borderRadius: 8,
        elevation: 2,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
    },
    headerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 12,
    },
    leftSection: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
    },
    statusButton: {
        marginRight: 12,
        padding: 4,
    },
    productInfo: {
        flex: 1,
    },
    productName: {
        fontSize: 16,
        fontWeight: 'bold',
        color: colors.BLACK,
        marginBottom: 2,
    },
    productSku: {
        fontSize: 12,
        color: colors.GREY,
        marginBottom: 2,
    },
    customerName: {
        fontSize: 12,
        color: colors.BLUE,
        fontStyle: 'italic',
    },
    rightSection: {
        alignItems: 'center',
        flexDirection: 'row',
    },
    quantityContainer: {
        alignItems: 'center',
        marginRight: 8,
    },
    quantityText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: colors.BLACK,
    },
    quantityLabel: {
        fontSize: 10,
        color: colors.GREY,
    },
    expandedContent: {
        padding: 12,
        paddingTop: 0,
        borderTopWidth: 1,
        borderTopColor: colors.LIGHT_GREY,
    },
    detailRow: {
        flexDirection: 'row',
        marginBottom: 8,
    },
    detailLabel: {
        fontSize: 14,
        fontWeight: 'bold',
        color: colors.BLACK,
        width: 100,
    },
    detailValue: {
        fontSize: 14,
        color: colors.BLACK,
        flex: 1,
    },
    actionButtons: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        marginTop: 12,
    },
    actionButton: {
        paddingHorizontal: 20,
        paddingVertical: 8,
        borderRadius: 6,
        minWidth: 120,
    },
    actionButtonText: {
        fontSize: 14,
        fontWeight: 'bold',
    },
});

export default ProductChecklistItem;
